<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, ref } from "vue";

// Props interface
interface Props {
  botHook: any; // Bot hook instance từ parent (như chat module)
}

const props = defineProps<Props>();

// Sử dụng botHook từ props thay vì individual props/emits
const { filterVisible, filterRef, handleFilter } = props.botHook;

const loading = ref(false);
const formRef = ref();

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Bot Name")),
    prop: "name",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Search by bot name"),
      clearable: true
    },
    colProps: { span: 12 }
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    fieldProps: {
      placeholder: $t("Filter by status"),
      clearable: true
    },
    options: [
      { label: $t("Active"), value: "active" },
      { label: $t("Inactive"), value: "inactive" }
    ],
    colProps: { span: 12 }
  }
];

// Không cần local functions vì đã có từ botHook
</script>

<template>
  <PlusDrawerForm ref="formRef" :visible="filterVisible.value" :model-value="filterRef.value" size="30%"
    :closeOnClickModal="true" :closeOnPressEscape="true" :showClose="true" :destroyOnClose="true" :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }" @update:visible="val => (filterVisible.value = val)" @update:model-value="val => (filterRef.value = val)">
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">{{ $t("Filter") }}</span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="
          () => {
            filterRef.value = { isTrashed: 'no' };
          }
        ">
          {{ $t("Reset") }}
        </el-button>
        <el-button type="primary" :loading="loading" @click="handleFilter(filterRef.value)">
          {{ $t("Apply Filter") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style scoped>
.custom-group-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  width: 100%;
}
</style>
