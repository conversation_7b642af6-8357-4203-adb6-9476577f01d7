import { http } from "@/utils/http";
import type { Result } from "@/utils/response";

// Public API endpoints (no authentication required)
export const getBots = () => {
  return http.request<r>("get", "/api/bots");
};

export const getBotByUuid = (uuid: string) => {
  return http.request<r>("get", `/api/bots/${uuid}`);
};

// Dropdown API for public use
export const getBotsDropdown = () => {
  return http.request<r>("get", "/api/bots/dropdown");
};
