import { http } from "@/utils/http";

type r = {
  success: boolean;
  data?: {
    list?: Array<any>;
    total?: number;
    [key: string]: any;
  };
  message?: string;
};

export const getKnowledgeBaseList = (params?: object) => {
  return http.request<r>("get", "/api/knowledge-bases", { params });
};

export const getKnowledgeBaseById = (id: number) => {
  return http.request<r>("get", `/api/knowledge-bases/${id}`);
};

export const uploadKnowledgeBaseFile = (formData: FormData) => {
  return http.request<r>("post", "/api/knowledge-bases/upload", {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

export const processKnowledgeBase = (id: number) => {
  return http.request<r>("post", `/api/knowledge-bases/${id}/process`);
};

export const searchKnowledgeBase = (params: object) => {
  return http.request<r>("get", "/api/knowledge-bases/search", { params });
};
