import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/bot/utils/type";
import type { Result } from "@/ultils/response";

type r = Result;

export const getBots = (params?: object) => {
  return http.request<Result>("get", "/api/auth/bots", {
    params
  });
};

export const getBotById = (id: number) => {
  return http.request<Result>("get", `/api/auth/bots/${id}`);
};

export const createBot = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/bots", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateBotById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/bots/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const destroyBotById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/bots/${id}/force`);
};

export const bulkDestroyBots = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/bots/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};
