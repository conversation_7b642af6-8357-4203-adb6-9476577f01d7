import { reactive, ref } from "vue";
import {
  getBots,
  createBot,
  updateBotById,
  deleteBotById,
  bulkDeleteBots,
  bulkDestroyBots,
  destroyBotById,
  restoreBotById,
  bulkRestoreBots
} from "../utils/auth-api";
import { useConvertKeyToCamel } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import type { BotFilterProps } from "@/views/bot/utils/type";

export function useBotHook() {
  // State management
  const loading = ref(false);
  const filterRef = ref();
  const records = ref([]);
  const multipleSelection = ref([]);
  const sort = ref({ prop: "id", order: "descending" });

  // Pagination
  const pagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 50, 100]
  });

  // Form states
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref({});
  const botFormRef = ref();

  // API Handlers
  const fnGetBots = async (params?: BotFilterProps) => {
    loading.value = true;
    try {
      const queryParams = {
        page: pagination.currentPage,
        per_page: pagination.pageSize,
        sort_by: sort.value.prop,
        sort_order: sort.value.order === "ascending" ? "asc" : "desc",
        ...params
      };

      const { data } = await getBots(queryParams);
      
      if (data?.success) {
        records.value = useConvertKeyToCamel(data.data.data || []);
        pagination.total = data.data.total || 0;
        pagination.currentPage = data.data.current_page || 1;
      }
    } catch (error) {
      console.error("Error fetching bots:", error);
      message($t("Failed to fetch bots"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  const fnHandleCreateBot = async (data: FieldValues) => {
    try {
      const response = await createBot(data);
      if (response.data?.success) {
        message($t("Bot created successfully"), { type: "success" });
        drawerVisible.value = false;
        fnGetBots();
        return true;
      }
    } catch (error) {
      console.error("Error creating bot:", error);
      message($t("Failed to create bot"), { type: "error" });
    }
    return false;
  };

  const fnHandleUpdateBot = async (id: number, data: FieldValues) => {
    try {
      const response = await updateBotById(id, data);
      if (response.data?.success) {
        message($t("Bot updated successfully"), { type: "success" });
        drawerVisible.value = false;
        fnGetBots();
        return true;
      }
    } catch (error) {
      console.error("Error updating bot:", error);
      message($t("Failed to update bot"), { type: "error" });
    }
    return false;
  };

  const fnHandleDelete = async (id: number) => {
    try {
      const response = await deleteBotById(id);
      if (response.data?.success) {
        message($t("Bot deleted successfully"), { type: "success" });
        fnGetBots();
        return true;
      }
    } catch (error) {
      console.error("Error deleting bot:", error);
      message($t("Failed to delete bot"), { type: "error" });
    }
    return false;
  };

  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      const response = await bulkDeleteBots(ids);
      if (response.data?.success) {
        message($t("Bots deleted successfully"), { type: "success" });
        fnGetBots();
        return true;
      }
    } catch (error) {
      console.error("Error bulk deleting bots:", error);
      message($t("Failed to delete bots"), { type: "error" });
    }
    return false;
  };

  // Table Event Handlers
  const fnHandleSelectionChange = (selection: any[]) => {
    multipleSelection.value = selection;
  };

  const fnHandleSortChange = ({ prop, order }: any) => {
    sort.value = { prop, order };
    fnGetBots();
  };

  const fnHandlePageChange = (page: number) => {
    pagination.currentPage = page;
    fnGetBots();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetBots();
  };

  // UI Action Handlers
  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this bot?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(row.id);
    } catch {
      // User cancelled
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected bots?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      await fnHandleBulkDelete(ids);
    } catch {
      // User cancelled
    }
  };

  const handleDestroy = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this bot?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );
      const response = await destroyBotById(row.id);
      if (response.data?.success) {
        message($t("Bot permanently deleted"), { type: "success" });
        fnGetBots();
      }
    } catch (error) {
      if (error !== "cancel") {
        message($t("Failed to permanently delete bot"), { type: "error" });
      }
    }
  };

  const handleBulkDestroy = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete selected bots?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDestroyBots(ids);
      if (response.data?.success) {
        message($t("Bots permanently deleted"), { type: "success" });
        fnGetBots();
      }
    } catch (error) {
      if (error !== "cancel") {
        message($t("Failed to permanently delete bots"), { type: "error" });
      }
    }
  };

  const handleRestore = async (row: any) => {
    try {
      const response = await restoreBotById(row.id);
      if (response.data?.success) {
        message($t("Bot restored successfully"), { type: "success" });
        fnGetBots();
      }
    } catch (error) {
      message($t("Failed to restore bot"), { type: "error" });
    }
  };

  const handleBulkRestore = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkRestoreBots(ids);
      if (response.data?.success) {
        message($t("Bots restored successfully"), { type: "success" });
        fnGetBots();
      }
    } catch (error) {
      message($t("Failed to restore bots"), { type: "error" });
    }
  };

  // Form Handlers
  const handleSubmit = async (values: FieldValues) => {
    const isEdit = !!values.id;
    const success = isEdit
      ? await fnHandleUpdateBot(values.id, values)
      : await fnHandleCreateBot(values);
    
    if (success && botFormRef.value?.resetForm) {
      botFormRef.value.resetForm();
    }
  };

  const handleFilter = async (values: FieldValues) => {
    filterVisible.value = false;
    pagination.currentPage = 1;
    await fnGetBots(values);
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    botFormRef,

    // API Handlers
    fnGetBots,
    fnHandleCreateBot,
    fnHandleUpdateBot,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
