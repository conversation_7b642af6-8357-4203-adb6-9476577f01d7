import { reactive, ref } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import type { BotFilterProps } from "@/views/bot/utils/type";
import {
  getBots,
  destroyBotById,
  bulkDestroyBots,
  deleteBotById,
  bulkDeleteBots,
  restoreBotById,
  bulkRestoreBots,
  createBot,
  updateBotById
} from "@/views/bot/utils/auth-api";

export function useBotHook() {
  // Data/State
  const loading = ref(false);
  const filterRef = ref<BotFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({
    status: "active"
  });
  const botFormRef = ref();

  // API Handlers
  const fnGetBots = async () => {
    loading.value = true;
    try {
      const response = await getBots(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;

      console.log("Bot records***************>", records.value);
    } catch (e) {
      console.error("Get Bot error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  // Table Event Handlers
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = async ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    await fnGetBots();
  };

  const fnHandlePageChange = async (val: number) => {
    pagination.currentPage = val;
    await fnGetBots();
  };

  const fnHandleSizeChange = async (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    await fnGetBots();
  };

  /*
   ***************************
   *   Delete handlers and actions
   ***************************
   */
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDelete(id);
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleDelete = async (id: number) => {
    try {
      loading.value = true;
      const response = await destroyBotById(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Delete failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleDestroy = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to destroy this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleDestroy(id);
    } catch {
      console.log("Destroy cancelled");
    }
  };
  const fnHandleDestroy = async (id: number) => {
    try {
      loading.value = true;
      const response = await deleteBotById(id);
      if (response.success) {
        message(response.message || $t("Destroy successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response.message || $t("Destroy failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Destroy Bot error:", error);
      message(error.response?.data?.message || $t("Destroy failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleRestore = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      await fnHandleRestore(id);
    } catch {
      console.log("Restore cancelled");
    }
  };
  const fnHandleRestore = async (id: number) => {
    try {
      loading.value = true;
      const response = await restoreBotById(id);
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Restore Bot error:", error);
      message(error.response?.data?.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleBulkDelete = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkDelete(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleBulkDelete = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkDeleteBots({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk delete Bots error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleBulkDestroy = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to delete"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkDestroy(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Delete cancelled");
    }
  };
  const fnHandleBulkDestroy = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkDestroyBots({ ids });
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk destroy Bots error:", error);
      message(error.response?.data?.message || $t("Delete failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleBulkRestore = async (tableRef?: any) => {
    const ids = multipleSelection.value.map(item => item.id);
    if (ids.length === 0) {
      message($t("Please select items to restore"), {
        type: "warning"
      });
      return;
    }
    try {
      await ElMessageBox.confirm(
        $t("Are you sure to restore selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const success = await fnHandleBulkRestore(ids);
      if (success && tableRef?.clearSelection) {
        tableRef.clearSelection();
      }
    } catch {
      console.log("Restore cancelled");
    }
  };
  const fnHandleBulkRestore = async (ids: number[]) => {
    try {
      loading.value = true;
      const response = await bulkRestoreBots({ ids });
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      console.error("Bulk restore Bots error:", error);
      message(error.response?.data?.message || $t("Restore failed"), {
        type: "error"
      });
      return false;
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   CTA handlers
   ***************************
   */
  const handleSubmit = async (values: FieldValues) => {
    if (values.id != null) {
      await fnHandleUpdateBot(Number(values.id), values);
      return;
    }
    const success = await fnHandleCreateBot(values);
    if (success) {
      drawerValues.value = { status: "active" };
      botFormRef.value?.resetForm();
    }
  };
  const fnHandleCreateBot = async (formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await createBot(formData);
      if (response.success) {
        message(response.message || $t("Create successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Create failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };
  const fnHandleUpdateBot = async (id: number, formData: FieldValues) => {
    try {
      loading.value = true;
      const response = await updateBotById(id, formData);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetBots();
        return true;
      }
      message(response?.message || $t("Update failed"), {
        type: "error"
      });
      return false;
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Update failed"),
        {
          type: "error"
        }
      );
      return false;
    } finally {
      loading.value = false;
    }
  };

  const handleFilter = async (values: BotFilterProps) => {
    filterRef.value = values;
    await fnGetBots();
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    filterVisible,
    drawerVisible,
    drawerValues,
    botFormRef,

    // API Handlers
    fnGetBots,
    fnHandleCreateBot,
    fnHandleUpdateBot,
    fnHandleDelete,
    fnHandleBulkDelete,

    // Table Event Handlers
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
