import { reactive, ref } from "vue";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";
import {
  getBots,
  deleteBotById,
  bulkDeleteBots,
  bulkDestroyBots,
  destroyBotById,
  restoreBotById,
  bulkRestoreBots,
  createBot,
  updateBotById
} from "@/views/bot/utils/auth-api";

export function useBotHook() {
  // State management
  const loading = ref(false);
  const filterRef = ref();
  const records = ref([]);
  const multipleSelection = ref([]);

  // Pagination
  const pagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 50, 100]
  });

  // Form states
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref({});
  const botFormRef = ref();

  // API Handlers
  const fnGetBots = async (params?: any) => {
    loading.value = true;
    try {
      const queryParams = {
        page: pagination.currentPage,
        per_page: pagination.pageSize,
        ...params
      };

      const { data } = await getBots(queryParams);

      if (data?.success) {
        records.value = useConvertKeyToCamel(data.data.data || []);
        pagination.total = data.data.total || 0;
        pagination.currentPage = data.data.current_page || 1;
      }
    } catch (error) {
      console.error("Error fetching bots:", error);
      message($t("Failed to fetch bots"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  // Table Event Handlers
  const fnHandleSelectionChange = (selection: any[]) => {
    multipleSelection.value = selection;
  };

  const fnHandlePageChange = (page: number) => {
    pagination.currentPage = page;
    fnGetBots();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetBots();
  };

  const fnHandleSortChange = ({ prop, order }: any) => {
    fnGetBots();
  };

  // UI Action Handlers
  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this bot?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const response = await deleteBotById(row.id);
      if (response.data?.success) {
        message($t("Bot deleted successfully"), { type: "success" });
        fnGetBots();
      }
    } catch (error) {
      if (error !== "cancel") {
        message($t("Failed to delete bot"), { type: "error" });
      }
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected bots?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDeleteBots(ids);
      if (response.data?.success) {
        message($t("Bots deleted successfully"), { type: "success" });
        fnGetBots();
      }
    } catch (error) {
      if (error !== "cancel") {
        message($t("Failed to delete bots"), { type: "error" });
      }
    }
  };

  const handleDestroy = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this bot?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );
      const response = await destroyBotById(row.id);
      if (response.data?.success) {
        message($t("Bot permanently deleted"), { type: "success" });
        fnGetBots();
      }
    } catch (error) {
      if (error !== "cancel") {
        message($t("Failed to permanently delete bot"), { type: "error" });
      }
    }
  };

  const handleBulkDestroy = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete selected bots?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDestroyBots(ids);
      if (response.data?.success) {
        message($t("Bots permanently deleted"), { type: "success" });
        fnGetBots();
      }
    } catch (error) {
      if (error !== "cancel") {
        message($t("Failed to permanently delete bots"), { type: "error" });
      }
    }
  };

  const handleRestore = async (row: any) => {
    try {
      const response = await restoreBotById(row.id);
      if (response.data?.success) {
        message($t("Bot restored successfully"), { type: "success" });
        fnGetBots();
      }
    } catch (error) {
      message($t("Failed to restore bot"), { type: "error" });
    }
  };

  const handleBulkRestore = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkRestoreBots(ids);
      if (response.data?.success) {
        message($t("Bots restored successfully"), { type: "success" });
        fnGetBots();
      }
    } catch (error) {
      message($t("Failed to restore bots"), { type: "error" });
    }
  };

  // Form Handlers
  const handleSubmit = async (values: FieldValues) => {
    const isEdit = !!values.id;
    try {
      const response = isEdit
        ? await updateBotById(values.id, values)
        : await createBot(values);

      if (response.data?.success) {
        message($t(isEdit ? "Bot updated successfully" : "Bot created successfully"), { type: "success" });
        drawerVisible.value = false;
        fnGetBots();
        if (botFormRef.value?.resetForm) {
          botFormRef.value.resetForm();
        }
      }
    } catch (error) {
      message($t(isEdit ? "Failed to update bot" : "Failed to create bot"), { type: "error" });
    }
  };

  const handleFilter = async (values: FieldValues) => {
    filterVisible.value = false;
    pagination.currentPage = 1;
    await fnGetBots(values);
  };

  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    filterVisible,
    drawerVisible,
    drawerValues,
    botFormRef,

    // API Handlers
    fnGetBots,
    fnHandlePageChange,
    fnHandleSizeChange,
    fnHandleSortChange,
    fnHandleSelectionChange,

    // UI Action Handlers
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,

    // Form Handlers
    handleSubmit,
    handleFilter
  };
}
