import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { $t } from "@/plugins/i18n";
import type { FormItemProps, KnowledgeBaseFilterProps } from "./type";
import {
  getKnowledgeBases,
  createKnowledgeBase,
  updateKnowledgeBase,
  deleteKnowledgeBase,
  bulkDeleteKnowledgeBases,
  destroyKnowledgeBase,
  bulkDestroyKnowledgeBases,
  restoreKnowledgeBase,
  bulkRestoreKnowledgeBases
} from "./auth-api";

export function useKnowledgeBaseHook() {
  const loading = ref(false);
  const knowledgeBaseFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  
  const filterRef = ref<KnowledgeBaseFilterProps>({
    isTrashed: "no"
  });
  
  let drawerValues = reactive<FormItemProps>({
    type: "text",
    status: "active"
  });

  const pagination = reactive({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    background: true,
    pageSizes: [10, 20, 50, 100]
  });

  const records = ref<FormItemProps[]>([]);
  const multipleSelection = ref<FormItemProps[]>([]);

  const fnGetKnowledgeBases = async () => {
    loading.value = true;
    try {
      const params = {
        page: pagination.currentPage,
        per_page: pagination.pageSize,
        ...filterRef.value
      };
      
      const { data } = await getKnowledgeBases(params);
      if (data?.list) {
        records.value = data.list;
        pagination.total = data.total || 0;
      }
    } catch (error) {
      console.error("Error fetching knowledge bases:", error);
      ElMessage.error($t("Failed to fetch knowledge bases"));
    } finally {
      loading.value = false;
    }
  };

  const fnHandleSelectionChange = (selection: FormItemProps[]) => {
    multipleSelection.value = selection;
  };

  const fnHandleSortChange = (sort: any) => {
    // Handle sort change
    fnGetKnowledgeBases();
  };

  const fnHandlePageChange = (page: number) => {
    pagination.currentPage = page;
    fnGetKnowledgeBases();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetKnowledgeBases();
  };

  const handleSubmit = async (values: FormItemProps) => {
    loading.value = true;
    try {
      if (values.id) {
        await updateKnowledgeBase(values.id, values);
        ElMessage.success($t("Knowledge base updated successfully"));
      } else {
        await createKnowledgeBase(values);
        ElMessage.success($t("Knowledge base created successfully"));
      }
      
      drawerVisible.value = false;
      Object.assign(drawerValues, { type: "text", status: "active" });
      fnGetKnowledgeBases();
    } catch (error) {
      console.error("Error saving knowledge base:", error);
      ElMessage.error($t("Failed to save knowledge base"));
    } finally {
      loading.value = false;
    }
  };

  const handleFilter = async (values?: KnowledgeBaseFilterProps) => {
    if (values) {
      Object.assign(filterRef.value, values);
    }
    pagination.currentPage = 1;
    filterVisible.value = false;
    fnGetKnowledgeBases();
  };

  const handleDestroy = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to move this knowledge base to trash?"),
        $t("Warning"),
        { type: "warning" }
      );
      
      await destroyKnowledgeBase(id);
      ElMessage.success($t("Knowledge base moved to trash"));
      fnGetKnowledgeBases();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error destroying knowledge base:", error);
        ElMessage.error($t("Failed to move knowledge base to trash"));
      }
    }
  };

  const handleBulkDestroy = async () => {
    if (multipleSelection.value.length === 0) return;
    
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to move selected knowledge bases to trash?"),
        $t("Warning"),
        { type: "warning" }
      );
      
      const ids = multipleSelection.value.map(item => item.id!);
      await bulkDestroyKnowledgeBases(ids);
      ElMessage.success($t("Knowledge bases moved to trash"));
      fnGetKnowledgeBases();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk destroying knowledge bases:", error);
        ElMessage.error($t("Failed to move knowledge bases to trash"));
      }
    }
  };

  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this knowledge base?"),
        $t("Warning"),
        { type: "warning" }
      );
      
      await deleteKnowledgeBase(id);
      ElMessage.success($t("Knowledge base deleted permanently"));
      fnGetKnowledgeBases();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error deleting knowledge base:", error);
        ElMessage.error($t("Failed to delete knowledge base"));
      }
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) return;
    
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete selected knowledge bases?"),
        $t("Warning"),
        { type: "warning" }
      );
      
      const ids = multipleSelection.value.map(item => item.id!);
      await bulkDeleteKnowledgeBases(ids);
      ElMessage.success($t("Knowledge bases deleted permanently"));
      fnGetKnowledgeBases();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk deleting knowledge bases:", error);
        ElMessage.error($t("Failed to delete knowledge bases"));
      }
    }
  };

  const handleRestore = async (id: number) => {
    try {
      await restoreKnowledgeBase(id);
      ElMessage.success($t("Knowledge base restored"));
      fnGetKnowledgeBases();
    } catch (error) {
      console.error("Error restoring knowledge base:", error);
      ElMessage.error($t("Failed to restore knowledge base"));
    }
  };

  const handleBulkRestore = async () => {
    if (multipleSelection.value.length === 0) return;
    
    try {
      const ids = multipleSelection.value.map(item => item.id!);
      await bulkRestoreKnowledgeBases(ids);
      ElMessage.success($t("Knowledge bases restored"));
      fnGetKnowledgeBases();
    } catch (error) {
      console.error("Error bulk restoring knowledge bases:", error);
      ElMessage.error($t("Failed to restore knowledge bases"));
    }
  };

  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    filterVisible,
    drawerVisible,
    drawerValues,
    knowledgeBaseFormRef,
    fnGetKnowledgeBases,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandlePageChange,
    fnHandleSizeChange,
    handleDelete,
    handleBulkDelete,
    handleDestroy,
    handleBulkDestroy,
    handleRestore,
    handleBulkRestore,
    handleSubmit,
    handleFilter
  };
}
