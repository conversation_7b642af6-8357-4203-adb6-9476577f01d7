export type FormItemProps = {
  id?: number | null;
  code?: string;
  name?: string;
  nativeName?: string;
  direction?: string;
  flag?: string;
  isActive?: boolean;
  isDefault?: boolean;
  status?: string;
  sortOrder?: number;
};

export type LanguageFilterProps = {
  code?: string;
  name?: string;
  nativeName?: string;
  direction?: string;
  isActive?: boolean;
  isDefault?: boolean;
  status?: string;
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
