<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);

const formRef = ref();

onMounted(() => { });

const form: PlusColumn[] = [
  {
    label: "Name",
    prop: "name",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter organization name"
    }
  },
  {
    label: "Description",
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: "Enter organization description"
    }
  },
  {
    label: "Website",
    prop: "website",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter website URL"
    }
  },
  {
    label: "Email",
    prop: "email",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter email address"
    }
  },
  {
    label: "Phone",
    prop: "phone",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter phone number"
    }
  },
  {
    label: "Address",
    prop: "address",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter address"
    }
  },
  {
    label: "City",
    prop: "city",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter city"
    }
  },
  {
    label: "State",
    prop: "state",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter state/province"
    }
  },
  {
    label: "Country",
    prop: "country",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter country"
    }
  },
  {
    label: "Postal Code",
    prop: "postalCode",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter postal code"
    }
  },
  {
    label: "Organization Type",
    prop: "organizationType",
    valueType: "select",
    options: [
      { label: "Company", value: "company" },
      { label: "Non-profit", value: "nonprofit" },
      { label: "Government", value: "government" },
      { label: "Educational", value: "educational" }
    ]
  },
  {
    label: "Industry",
    prop: "industry",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter industry"
    }
  },
  {
    label: "Employee Count",
    prop: "employeeCount",
    valueType: "number",
    fieldProps: {
      placeholder: "Enter employee count"
    }
  },
  {
    label: "Founded Year",
    prop: "foundedYear",
    valueType: "number",
    fieldProps: {
      placeholder: "Enter founded year"
    }
  },
  {
    label: "Status",
    prop: "status",
    valueType: "select",
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
      { label: "Suspended", value: "suspended" }
    ]
  },
  {
    label: "Is Verified",
    prop: "isVerified",
    valueType: "switch"
  }
];

const rules = {
  name: [
    {
      required: true,
      message: "Please enter organization name",
      trigger: "blur"
    }
  ],
  email: [
    { required: true, message: "Please enter email", trigger: "blur" },
    { type: "email", message: "Please enter valid email", trigger: "blur" }
  ],
  organizationType: [
    {
      required: true,
      message: "Please select organization type",
      trigger: "change"
    }
  ],
  status: [
    { required: true, message: "Please select status", trigger: "change" }
  ]
};
</script>
