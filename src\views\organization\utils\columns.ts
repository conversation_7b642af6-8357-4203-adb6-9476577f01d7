import dayjs from "dayjs";
import { $t } from "@/plugins/i18n";
import { ElTag } from "element-plus";
import { h } from "vue";
import { capitalized } from "@/utils/helpers";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "code",
    align: "center",
    sortable: true,
    width: 90,
    headerRenderer: () => $t("code")
  },
  {
    prop: "name",
    align: "left",
    sortable: true,
    minWidth: 160,
    headerRenderer: () => $t("Language Name")
  },
  {
    prop: "nativeName",
    align: "left",
    sortable: true,
    minWidth: 160,
    headerRenderer: () => $t("Language Native Name")
  },
  {
    prop: "direction",
    align: "center",
    sortable: true,
    width: 160,
    headerRenderer: () => $t("Direction"),
    cellRenderer: ({ row }) => row.direction.toUpperCase()
  },
  {
    prop: "status",
    align: "center",
    sortable: true,
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.status === "active" ? "success" : "danger",
          size: "small"
        },
        () => $t(capitalized(row.status)).toUpperCase()
      );
    }
  },
  {
    prop: "createdAt",
    width: 160,
    sortable: true,
    headerRenderer: () => $t("Created at"),
    formatter: ({ createdAt }) =>
      createdAt ? dayjs(createdAt).format("YYYY-MM-DD HH:mm") : "-"
  },
  {
    label: "",
    fixed: "right",
    width: 140,
    slot: "operation",
    sortable: false
  }
];
