export type FormItemProps = {
  id?: number | null;
  uuid?: string;
  ownerType?: string;
  ownerId?: number;
  name?: string;
  type?: "file" | "text" | "url" | "document";
  storagePath?: string;
  content?: string;
  status?: "active" | "inactive" | "processing" | "failed";
  metadata?: string;
  createdAt?: string;
  updatedAt?: string;
};

export type KnowledgeBaseFilterProps = {
  name?: string;
  type?: "file" | "text" | "url" | "document" | "";
  status?: "active" | "inactive" | "processing" | "failed" | "";
  ownerType?: string;
  isTrashed?: "yes" | "no";
};

export type KnowledgeBaseHookType = {
  loading: any;
  filterRef: any;
  pagination: any;
  records: any;
  multipleSelection: any;
  filterVisible: any;
  drawerVisible: any;
  drawerValues: any;
  knowledgeBaseFormRef: any;
  fnGetKnowledgeBases: () => void;
  fnHandleSelectionChange: (selection: any[]) => void;
  fnHandleSortChange: (sort: any) => void;
  fnHandlePageChange: (page: number) => void;
  fnHandleSizeChange: (size: number) => void;
  handleDelete: (id: number) => void;
  handleBulkDelete: () => void;
  handleDestroy: (id: number) => void;
  handleBulkDestroy: () => void;
  handleRestore: (id: number) => void;
  handleBulkRestore: () => void;
  handleSubmit: (values: FormItemProps) => void;
  handleFilter: (values?: KnowledgeBaseFilterProps) => void;
};
