<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);

const formRef = ref();

onMounted(() => { });

const form: PlusColumn[] = [
  {
    label: "Name",
    prop: "name",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter knowledge base name"
    }
  },
  {
    label: "Type",
    prop: "type",
    valueType: "select",
    options: [
      { label: "File", value: "file" },
      { label: "Text", value: "text" },
      { label: "URL", value: "url" },
      { label: "Document", value: "document" }
    ]
  },
  {
    label: "Owner Type",
    prop: "ownerType",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter owner type (e.g., user, organization)"
    }
  },
  {
    label: "Owner ID",
    prop: "ownerId",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter owner ID"
    }
  },
  {
    label: "Storage Path",
    prop: "storagePath",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter storage path (for file type)"
    }
  },
  {
    label: "Content",
    prop: "content",
    valueType: "textarea",
    fieldProps: {
      placeholder: "Enter content (for text type)",
      rows: 6
    }
  },
  {
    label: "Status",
    prop: "status",
    valueType: "select",
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
      { label: "Processing", value: "processing" },
      { label: "Failed", value: "failed" }
    ]
  },
  {
    label: "Metadata",
    prop: "metadata",
    valueType: "textarea",
    fieldProps: {
      placeholder: "Enter metadata (JSON format)",
      rows: 4
    }
  }
];

const rules = {
  name: [
    {
      required: true,
      message: "Please enter knowledge base name",
      trigger: "blur"
    }
  ],
  type: [{ required: true, message: "Please select type", trigger: "change" }],
  ownerType: [
    { required: true, message: "Please enter owner type", trigger: "blur" }
  ],
  ownerId: [
    { required: true, message: "Please enter owner ID", trigger: "blur" }
  ],
  status: [
    { required: true, message: "Please select status", trigger: "change" }
  ]
};

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm ref="formRef" :visible="props.visible" :model-value="props.values" size="40%"
    :closeOnClickModal="true" :closeOnPressEscape="true" :showClose="true" :destroyOnClose="true" :form="{
      columns: form,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }" @update:visible="val => emit('update:visible', val)" @update:model-value="val => emit('update:values', val)"
    @close="resetForm">
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Knowledge Base Information") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button plain type="primary" :loading="loading" :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(props.values)">
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
