<template>
  <plus-drawer-form
    ref="ruleFormRef"
    v-model:visible="drawerVisible"
    v-model="drawerValues"
    :form="form"
    :rules="rules"
    :loading="loading"
    @confirm="handleSubmit"
  />
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { FormItemProps } from "@/views/knowledge-base/utils/type";
import type { PlusColumn } from "plus-pro-components";

interface Props {
  knowledgeBaseHook: any;
}

const props = defineProps<Props>();

const {
  loading,
  drawerVisible,
  drawerValues,
  handleSubmit
} = props.knowledgeBaseHook;

const ruleFormRef = computed(() => props.knowledgeBaseHook.knowledgeBaseFormRef);

const form: PlusColumn[] = [
  {
    label: "Name",
    prop: "name",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter knowledge base name"
    }
  },
  {
    label: "Type",
    prop: "type",
    valueType: "select",
    options: [
      { label: "File", value: "file" },
      { label: "Text", value: "text" },
      { label: "URL", value: "url" },
      { label: "Document", value: "document" }
    ]
  },
  {
    label: "Owner Type",
    prop: "ownerType",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter owner type (e.g., user, organization)"
    }
  },
  {
    label: "Owner ID",
    prop: "ownerId",
    valueType: "number",
    fieldProps: {
      placeholder: "Enter owner ID"
    }
  },
  {
    label: "Storage Path",
    prop: "storagePath",
    valueType: "text",
    fieldProps: {
      placeholder: "Enter storage path (for file type)"
    }
  },
  {
    label: "Content",
    prop: "content",
    valueType: "textarea",
    fieldProps: {
      placeholder: "Enter content (for text type)",
      rows: 6
    }
  },
  {
    label: "Status",
    prop: "status",
    valueType: "select",
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
      { label: "Processing", value: "processing" },
      { label: "Failed", value: "failed" }
    ]
  },
  {
    label: "Metadata",
    prop: "metadata",
    valueType: "textarea",
    fieldProps: {
      placeholder: "Enter metadata (JSON format)",
      rows: 4
    }
  }
];

const rules = {
  name: [{ required: true, message: "Please enter knowledge base name", trigger: "blur" }],
  type: [{ required: true, message: "Please select type", trigger: "change" }],
  ownerType: [{ required: true, message: "Please enter owner type", trigger: "blur" }],
  ownerId: [{ required: true, message: "Please enter owner ID", trigger: "blur" }],
  status: [{ required: true, message: "Please select status", trigger: "change" }]
};
</script>
