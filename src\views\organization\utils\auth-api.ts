import { http } from "@/utils/http";
import type { Result } from "@/utils/response";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "@/views/organization/utils/type";

type r = Result;

export const getOrganizations = (params?: object) => {
  return http.request<Result>("get", "/api/auth/organizations", {
    params
  });
};

export const getOrganizationById = (id: number) => {
  return http.request<Result>("get", `/api/auth/organizations/${id}`);
};

export const createOrganization = (data: FormItemProps) => {
  return http.request<Result>("post", "/api/auth/organizations", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateOrganizationById = (id: number, data: FormItemProps) => {
  return http.request<Result>("put", `/api/auth/organizations/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const deleteOrganizationById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/organizations/${id}`);
};

export const bulkDeleteOrganizations = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/organizations/bulk-delete", {
    data
  });
};

export const destroyOrganizationById = (id: number) => {
  return http.request<Result>("delete", `/api/auth/organizations/${id}/force`);
};

export const bulkDestroyOrganizations = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/organizations/bulk-destroy", {
    data
  });
};

export const restoreOrganizationById = (id: number) => {
  return http.request<Result>("put", `/api/auth/organizations/${id}/restore`);
};

export const bulkRestoreOrganizations = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/organizations/bulk-restore", {
    data
  });
};

export const dropdownOrganizations = () => {
  return http.request<Result>("get", "/api/auth/organizations/dropdown");
};
