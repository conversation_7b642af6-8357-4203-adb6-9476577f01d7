<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, onMounted, ref, watch, h } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getAiModels } from "../utils/auth-api";
import { FileUpload } from "@/components/Shared";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  isEdit?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);
const formRef = ref();
const aiModels = ref([]);

onMounted(async () => {
  console.log("Bot------------>:", props.values);
  await loadAiModels();
});

const loadAiModels = async () => {
  try {
    const { data } = await getAiModels();
    if (data?.success) {
      aiModels.value = data.data.map((model: any) => ({
        label: model.name,
        value: model.id
      }));
    }
  } catch (error) {
    console.error("Error loading AI models:", error);
  }
};

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("Bot Name")),
    prop: "name",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input bot name"),
        trigger: ["blur"]
      },
      {
        min: 1,
        max: 120,
        message: $t("Length must be between 1 and 120 characters"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: $t("Enter bot name"),
      maxlength: 120,
      showWordLimit: true
    }
  },
  {
    label: computed(() => $t("Logo")),
    prop: "logo",
    valueType: "input",
    fieldProps: {
      placeholder: $t("Enter logo URL or upload image")
    },
    renderField: ({ model }) => {
      return h("div", { class: "space-y-2" }, [
        h("el-input", {
          modelValue: model.logo,
          placeholder: $t("Enter logo URL"),
          clearable: true,
          "onUpdate:modelValue": (value: string) => {
            model.logo = value;
          }
        }),
        h("div", { class: "text-center text-gray-500 text-sm" }, $t("or")),
        h(FileUpload, {
          modelValue: model.logo,
          accept: "image/*",
          maxSize: 5,
          listType: "picture-card",
          showFileList: false,
          "onUpdate:modelValue": (value: string) => {
            model.logo = value;
          }
        })
      ]);
    }
  },
  {
    label: computed(() => $t("Description")),
    prop: "description",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Enter bot description"),
      showWordLimit: true,
      autosize: { minRows: 2, maxRows: 4 }
    }
  },
  {
    label: computed(() => $t("AI Model")),
    prop: "aiModelId",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select an AI model"),
        trigger: ["change"]
      }
    ],
    fieldProps: {
      placeholder: $t("Select AI model"),
      clearable: true
    },
    options: computed(() => aiModels.value)
  },
  {
    label: computed(() => $t("System Prompt")),
    prop: "systemPrompt",
    valueType: "textarea",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input system prompt"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: $t("Enter system prompt that shapes the bot's behavior"),
      showWordLimit: true,
      autosize: { minRows: 3, maxRows: 6 }
    }
  },
  {
    label: computed(() => $t("Greeting Message")),
    prop: "greetingMessage",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Enter greeting message"),
      showWordLimit: true,
      autosize: { minRows: 2, maxRows: 4 }
    }
  },
  {
    label: computed(() => $t("Closing Message")),
    prop: "closingMessage",
    valueType: "textarea",
    fieldProps: {
      placeholder: $t("Enter closing message"),
      showWordLimit: true,
      autosize: { minRows: 2, maxRows: 4 }
    }
  },
  {
    label: computed(() => $t("Tool Calling Mode")),
    prop: "toolCallingMode",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select tool calling mode"),
        trigger: ["change"]
      }
    ],
    fieldProps: {
      placeholder: $t("Select tool calling mode")
    },
    options: [
      { label: $t("Auto"), value: "auto" },
      { label: $t("None"), value: "none" },
      { label: $t("Required"), value: "required" }
    ]
  },
  {
    label: computed(() => $t("Visibility")),
    prop: "visibility",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select visibility"),
        trigger: ["change"]
      }
    ],
    fieldProps: {
      placeholder: $t("Select visibility")
    },
    options: [
      { label: $t("Public"), value: "public" },
      { label: $t("Private"), value: "private" }
    ]
  },
  {
    label: computed(() => $t("Bot Type")),
    prop: "botType",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select bot type"),
        trigger: ["change"]
      }
    ],
    fieldProps: {
      placeholder: $t("Select bot type")
    },
    options: [
      { label: $t("Personal"), value: "personal" },
      { label: $t("Organization"), value: "organization" }
    ]
  },
  {
    label: computed(() => $t("Status")),
    prop: "status",
    valueType: "select",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please select status"),
        trigger: ["change"]
      }
    ],
    fieldProps: {
      placeholder: $t("Select status")
    },
    options: [
      { label: $t("Draft"), value: "draft" },
      { label: $t("Review"), value: "review" },
      { label: $t("Active"), value: "active" },
      { label: $t("Paused"), value: "paused" },
      { label: $t("Banned"), value: "banned" }
    ]
  }
];

const handleSubmit = async (values: FieldValues) => {
  try {
    loading.value = true;
    emit("submit", values);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000); // delay 3 seconds
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="60%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Bot Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit(values)"
        >
          {{ $t("Submit") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style scoped>
.custom-group-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
  width: 100%;
}
</style>
