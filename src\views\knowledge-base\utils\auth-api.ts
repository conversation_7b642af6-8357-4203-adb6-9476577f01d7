import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps } from "./type";

type r = {
  success: boolean;
  data?: {
    list?: Array<any>;
    total?: number;
    [key: string]: any;
  };
  message?: string;
};

export const getKnowledgeBases = (params?: object) => {
  return http.request<r>("get", "/api/auth/knowledge-bases", { params });
};

export const createKnowledgeBase = (data: FormItemProps) => {
  return http.request<r>("post", "/api/auth/knowledge-bases", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateKnowledgeBase = (id: number, data: FormItemProps) => {
  return http.request<r>("put", `/api/auth/knowledge-bases/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

export const deleteKnowledgeBase = (id: number) => {
  return http.request<r>("delete", `/api/auth/knowledge-bases/${id}`);
};

export const bulkDeleteKnowledgeBases = (ids: number[]) => {
  return http.request<r>("delete", "/api/auth/knowledge-bases/bulk", {
    data: { ids }
  });
};

export const destroyKnowledgeBase = (id: number) => {
  return http.request<r>("delete", `/api/auth/knowledge-bases/${id}/destroy`);
};

export const bulkDestroyKnowledgeBases = (ids: number[]) => {
  return http.request<r>("delete", "/api/auth/knowledge-bases/bulk-destroy", {
    data: { ids }
  });
};

export const restoreKnowledgeBase = (id: number) => {
  return http.request<r>("post", `/api/auth/knowledge-bases/${id}/restore`);
};

export const bulkRestoreKnowledgeBases = (ids: number[]) => {
  return http.request<r>("post", "/api/auth/knowledge-bases/bulk-restore", {
    data: { ids }
  });
};
