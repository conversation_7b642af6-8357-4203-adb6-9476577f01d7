<script setup lang="ts">
import { ElMessage, ElMessageBox, UploadFile, UploadFiles } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { reactive, ref, onMounted } from "vue";
import { useLanguageStoreHook } from "@/store/modules/language";
import {
  Plus,
  Refresh,
  Promotion,
  Delete,
  UploadFilled
} from "@element-plus/icons-vue";
import { $t } from "@/plugins/i18n";

import {
  createBot,
  getAiModelDropdown,
  getBotById,
  getGeneralPrompts,
  updateBotById
} from "../utils/auth-api";
import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import { message } from "@/utils/message";
import { useRoute } from "vue-router";
import { getToken } from "@/utils/auth";

// --- State Management ---
const route = useRoute();
const aiModels = ref([]);
const knowledgeTab = ref("files");
const formRef = ref<FormInstance>();

const loading = reactive({
  prompt: false,
  greeting: false,
  starters: false,
  submit: false
});

const fileLibrary = ref([
  {
    id: 1,
    name: "Giao_an_Ngu_van_lop_7.docx",
    type: "DOCX",
    date: "2025-05-20"
  },
  {
    id: 2,
    name: "Quy_che_tuyen_sinh_DH_2025.pdf",
    type: "PDF",
    date: "2025-04-15"
  }
]);

const getDefaultConfig = () => ({
  id: "agent_" + Date.now(),
  model: "",
  name: "",
  language: useLanguageStoreHook().locale,
  description: "",
  systemPrompt: "",
  greetingMessage: $t("Hello! How can I help you?"),
  starterMessages: [""],
  avatarUrl: null as string | null,
  logo: null,
  knowledge: {
    enabled: true,
    newUploads: [],
    libraryFiles: [],
    text: ""
  }
});

let agentConfig = reactive(getDefaultConfig());

const rules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: $t("Please enter AI Assistant Name"),
      trigger: "blur"
    },
    {
      min: 3,
      max: 50,
      message: $t("Length from 3 to 50 characters"),
      trigger: "blur"
    }
  ],
  model: [
    {
      required: true,
      message: $t("Please select LLM model"),
      trigger: "blur"
    }
  ],
  systemPrompt: [
    {
      required: true,
      message: $t("Please enter System Prompt"),
      trigger: "blur"
    },
    {
      min: 20,
      message: $t("Prompt needs at least 20 characters to be effective"),
      trigger: "blur"
    }
  ]
});

// --- API Call Logic ---
const callGeneralPrompts = async (params: object) => {
  try {
    const { data, success } = await getGeneralPrompts(params);

    if (success) {
      return data;
    }
    console.error("Invalid response structure from General Prompts API:", data);
    return null;
  } catch (error) {
    console.error("General Prompts API Error:", error);
    return null;
  }
};

const handleGetAiModelDropdown = async () => {
  try {
    const { data, success } = await getAiModelDropdown();
    console.log("AI Models Data:", success);
    if (success) {
      aiModels.value = data.map((model: any) => ({
        value: model.value,
        label: `${model.label} (${model.value})`
      }));
    }
  } catch (error) {
    console.error("Error loading AI models:", error);
  }
};

// --- Component Methods ---
const handleAvatarChange = (uploadFile: any) => {
  agentConfig.avatarUrl = URL.createObjectURL(uploadFile.raw);
  agentConfig.logo = uploadFile.raw;
};

const addStarter = () => agentConfig.starterMessages.push("");

const removeStarter = (index: number) =>
  agentConfig.starterMessages.splice(index, 1);

const handleLibrarySelectionChange = (selection: any[]) => {
  agentConfig.knowledge.libraryFiles = selection;
};

const handleFileChange = (uploadFile: UploadFile, uploadFiles: UploadFiles) => {
  const isDuplicate =
    uploadFiles.filter(
      (file: any) =>
        file.name === uploadFile.name && file.size === uploadFile.size
    ).length > 1;

  if (isDuplicate) {
    ElMessage.warning(
      $t('File "{fileName}" is already in the list.', {
        fileName: uploadFile.name
      })
    );
    agentConfig.knowledge.newUploads.pop();
  }
};

const resetForm = () => {
  ElMessageBox.confirm(
    $t("Are you sure you want to clear all current configuration?"),
    $t("Warning"),
    {
      confirmButtonText: $t("Confirm"),
      cancelButtonText: $t("Cancel"),
      type: "warning"
    }
  )
    .then(() => {
      Object.assign(agentConfig, getDefaultConfig());
      formRef.value?.resetFields();
      ElMessage.info($t("Form has been reset."));
    })
    .catch(() => {
      // Handle cancel action
    });
};

const generatePrompt = async () => {
  try {
    const { value } = await ElMessageBox.prompt(
      $t("Briefly describe the role of the Agent:"),
      $t("✨ Prompt Generator Assistant"),
      {
        confirmButtonText: $t("Generate"),
        cancelButtonText: $t("Cancel"),
        inputPlaceholder: $t(
          "Example: Vietnamese literature lesson planning assistant"
        )
      }
    );

    if (!value) return;

    loading.prompt = true;
    const result = await callGeneralPrompts({
      type: "system_prompt",
      role: value
    });

    if (result) {
      agentConfig.systemPrompt = result.trim();
      ElMessage.success($t("Prompt generated successfully!"));
      await formRef.value?.validateField("systemPrompt");
    }
  } catch (e) {
    if (e !== "cancel") {
      console.error("Error generating prompt:", e);
    }
  } finally {
    loading.prompt = false;
  }
};

const generateGreeting = async () => {
  if (!agentConfig.name) {
    ElMessage.warning($t("Please enter AI Assistant Name first."));
    return;
  }

  loading.greeting = true;
  try {
    const result = await callGeneralPrompts({
      type: "greeting_message",
      name: agentConfig.name
    });
    if (result) {
      agentConfig.greetingMessage = result.replace(/"/g, "").trim();
    }
  } catch (e) {
    console.error("Error generating greeting:", e);
  } finally {
    loading.greeting = false;
  }
};

const generateStarters = async () => {
  if (!agentConfig.systemPrompt) {
    ElMessage.warning($t("Please create System Prompt for best suggestions."));
    return;
  }

  loading.starters = true;
  try {
    const result = await callGeneralPrompts({
      type: "starting_message",
      system_prompt: agentConfig.systemPrompt
    });
    if (result) {
      try {
        const cleanedResult = result.match(/\[.*\]/s)?.[0] || result;
        const starters = JSON.parse(cleanedResult);
        if (
          Array.isArray(starters) &&
          starters.every(s => typeof s === "string")
        ) {
          agentConfig.starterMessages = starters.slice(0, 4);
        } else {
          ElMessage.error($t("AI returned data not in string array format."));
        }
      } catch (e) {
        console.error("JSON parsing error:", e, "Raw result:", result);
        ElMessage.error($t("Cannot parse suggestions from AI."));
      }
    }
  } catch (e) {
    console.error("Error generating starters:", e);
  } finally {
    loading.starters = false;
  }
};

const saveAndDeploy = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.submit = true;

    if (!agentConfig.uuid) {
      return await createBot(useConvertKeyToSnake(agentConfig))
        .then((response: any) => {
          if (response?.success) {
            message(response.message, { type: "success" });
            Object.assign(agentConfig, getDefaultConfig());
            formRef.value?.resetFields();
          }
        })
        .catch(e => {
          message(e.message, { type: "error" });
          console.log("e---------->", e);
        })
        .finally(() => (loading.submit = false));
    }

    return await updateBotById(
      route.params?.uuid,
      useConvertKeyToSnake(agentConfig)
    )
      .then((response: any) => {
        if (response?.success) {
          message(response.message, { type: "success" });
        }
      })
      .catch(e => {})
      .finally(() => (loading.submit = false));
  } catch (error) {
    console.log("Validation failed:", error);
    ElMessage.error($t("Please fill in all required fields."));
  }
};

const getBot = async () => {
  console.log("-------------->:", route.params.uuid);
  await getBotById(route.params?.uuid)
    .then((response: any) => {
      if (response?.success) {
        const botData = useConvertKeyToCamel(response.data);
        botData.model = botData.aiModel.key;
        botData.starterMessages = response.data.starter_messages ?? [""];
        botData.avatarUrl = botData.logo;

        Object.assign(agentConfig, botData);
      }
    })
    .catch(e => {
      console.log("getBot E------------------>:", e);
    })
    .finally();
};

// Trong methods hoặc setup
const handleUploadSuccess = (response, uploadFile, uploadFiles) => {
  console.log("Upload thành công:", response);
  console.log("File đã upload:", uploadFile);
  console.log("Danh sách tất cả files:", uploadFiles);

  // response chứa dữ liệu trả về từ server
  // Ví dụ: response.data, response.fileId, etc.
};

const handleUploadError = (error, uploadFile, uploadFiles) => {
  console.error("Upload thất bại:", error);
};

onMounted(async () => {
  await handleGetAiModelDropdown();
  if (route.params?.uuid) {
    await getBot();
  }
});
</script>

<template>
  <div class="">
    <header class="mb-8 flex justify-between items-center">
      <h1 class="text-3xl font-bold text-gray-800">
        {{ $t("AI Agent Studio") }}
      </h1>
      <div>
        <el-button :icon="Refresh" size="large" @click="resetForm">
          {{ $t("New Agent") }}
        </el-button>
        <el-button
          type="primary"
          :icon="Promotion"
          size="large"
          :loading="loading.submit"
          @click="saveAndDeploy"
        >
          {{ $t("Save & Deploy") }}
        </el-button>
      </div>
    </header>

    <div class="main-grid">
      <!-- Left Column -->
      <div class="left-column space-y-6">
        <div class="card">
          <h2 class="section-title !block text-center">{{ $t("Avatar") }}</h2>
          <div class="flex justify-center">
            <el-upload
              class="avatar-uploader"
              action="#"
              :show-file-list="false"
              :auto-upload="false"
              @change="handleAvatarChange"
            >
              <img
                v-if="agentConfig.avatarUrl"
                :src="agentConfig.avatarUrl"
                class="avatar"
                alt="avatar"
              />
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </div>
          <p class="text-xs text-center text-gray-500 !mt-4">
            {{ $t("Upload JPG, PNG, JPEG images. Size under 5MB.") }}
          </p>
        </div>

        <div class="card">
          <h2 class="section-title">{{ $t("Advanced Settings") }}</h2>
          <el-form-item :label="$t('Use Knowledge Base')">
            <el-switch v-model="agentConfig.knowledge.enabled" size="large" />
          </el-form-item>
          <p class="text-xs text-gray-500">
            {{
              $t(
                "Enable this feature to allow Agent access to your private knowledge sources."
              )
            }}
          </p>
        </div>
      </div>

      <!-- Right Column -->
      <div class="right-column space-y-6">
        <el-form
          ref="formRef"
          :model="agentConfig"
          :rules="rules"
          label-position="top"
          require-asterisk-position="right"
          size="default"
          class="flex flex-col gap-2"
        >
          <div class="card">
            <h2 class="section-title">{{ $t("Basic Information") }}</h2>
            <el-row :gutter="20">
              <el-col :xs="24" :sm="8">
                <el-form-item :label="$t('AI Assistant Name')" prop="name">
                  <el-input
                    v-model="agentConfig.name"
                    clearable
                    :placeholder="
                      $t('Example: Administrative Procedure Consultant')
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8">
                <el-form-item :label="$t('AI Model')" prop="model">
                  <el-select v-model="agentConfig.model" class="w-full">
                    <el-option
                      v-for="model in aiModels"
                      :key="model.value"
                      :label="model.label"
                      :value="model.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8">
                <el-form-item :label="$t('Language')">
                  <el-select v-model="agentConfig.language" class="w-full">
                    <el-option :label="$t('Vietnamese')" value="vi" />
                    <el-option :label="$t('English')" value="en" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item :label="$t('Description')">
              <el-input
                v-model="agentConfig.description"
                type="textarea"
                :placeholder="
                  $t('Brief description of AI Assistant functions.')
                "
              />
            </el-form-item>
          </div>

          <div class="card">
            <div class="flex flex-row items-center justify-between">
              <h2 class="section-title">{{ $t("AI Brain") }}</h2>
              <el-button
                class="gemini-button"
                :loading="loading.prompt"
                round
                @click="generatePrompt"
              >
                {{ $t("✨ Prompt Generator Assistant") }}
              </el-button>
            </div>
            <el-form-item
              :label="$t('Suggestion - Prompt')"
              prop="systemPrompt"
            >
              <el-input
                v-model="agentConfig.systemPrompt"
                type="textarea"
                :rows="8"
                :placeholder="$t('This is the most important part...')"
              />
            </el-form-item>
          </div>

          <div class="card">
            <h2 class="section-title">{{ $t("Chat Interface") }}</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div class="space-y-6">
                <el-form-item :label="$t('Welcome Message')">
                  <el-input
                    v-model="agentConfig.greetingMessage"
                    :placeholder="$t('Example: Hello! How can I help you?')"
                    type="textarea"
                    :rows="3"
                  >
                    <template #append>
                      <el-button
                        class="gemini-button"
                        :loading="loading.greeting"
                        @click="generateGreeting"
                      >
                        {{ $t("✨ Generate") }}
                      </el-button>
                    </template>
                  </el-input>
                </el-form-item>

                <el-form-item>
                  <template #label>
                    <div class="flex justify-between w-full items-center">
                      <span>{{ $t("Starter Suggestions") }}</span>
                      <el-button
                        class="gemini-button"
                        :loading="loading.starters"
                        round
                        size="small"
                        @click="generateStarters"
                      >
                        {{ $t("✨ Generate") }}
                      </el-button>
                    </div>
                  </template>
                  <div
                    v-for="(starter, index) in agentConfig.starterMessages"
                    :key="index"
                    class="flex items-center mb-2 w-full"
                  >
                    <el-input
                      v-model="agentConfig.starterMessages[index]"
                      :placeholder="$t('Example: What is the tuition fee?')"
                      class="flex-1 mr-1"
                    />
                    <el-button
                      type="danger"
                      :icon="Delete"
                      circle
                      plain
                      size="small"
                      @click="removeStarter(index)"
                    />
                  </div>
                  <el-button :icon="Plus" size="small" @click="addStarter">
                    {{ $t("Add Suggestion") }}
                  </el-button>
                </el-form-item>
              </div>

              <div>
                <h3 class="text-sm font-medium text-gray-500 mb-2 text-center">
                  {{ $t("Preview") }}
                </h3>
                <div class="chat-preview-container">
                  <div class="chat-preview-window">
                    <div class="chat-preview-header">
                      <img
                        :src="
                          agentConfig.avatarUrl ||
                          'https://placehold.co/40x40/E2E8F0/4A5568?text=AI'
                        "
                        class="chat-preview-avatar"
                        alt="logo"
                      />
                      <span class="font-semibold text-gray-700">
                        {{ agentConfig.name || $t("AI Assistant") }}
                      </span>
                    </div>
                    <div class="chat-preview-body">
                      <div class="chat-bubble">
                        <span>{{ agentConfig.greetingMessage }}</span>
                      </div>
                      <div
                        v-if="agentConfig.starterMessages?.some(s => s)"
                        class="starter-pills-container"
                      >
                        <div
                          v-for="starter in agentConfig.starterMessages?.filter(
                            s => s
                          )"
                          :key="starter"
                          class="starter-pill"
                        >
                          {{ starter }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="agentConfig.knowledge.enabled" class="card">
            <h2 class="section-title">{{ $t("Knowledge Base") }}</h2>
            <el-tabs v-model="knowledgeTab" type="border-card">
              <el-tab-pane :label="$t('Upload New Files')" name="files">
                <el-upload
                  v-model:file-list="agentConfig.knowledge.newUploads"
                  class="w-full"
                  drag
                  action="/api/auth/knowledge-bases/upload"
                  multiple
                  :auto-upload="true"
                  :headers="{
                    Authorization: `Bearer ${getToken().accessToken ?? getToken()}`,
                    'X-Requested-With': 'XMLHttpRequest'
                  }"
                  @change="handleFileChange"
                  @success="handleUploadSuccess"
                  @error="handleUploadError"
                >
                  <el-icon class="el-icon--upload">
                    <upload-filled />
                  </el-icon>
                  <div class="el-upload__text">
                    {{ $t("Drag files here or") }}
                    <em>{{ $t("click to upload") }}</em>
                  </div>
                </el-upload>
              </el-tab-pane>

              <el-tab-pane :label="$t('Text')" name="text">
                <el-input
                  v-model="agentConfig.knowledge.text"
                  type="textarea"
                  :rows="10"
                  :placeholder="$t('Paste text content here.')"
                />
              </el-tab-pane>

              <el-tab-pane :label="$t('Choose from Library')" name="library">
                <p class="text-sm text-gray-500 mb-4">
                  {{
                    $t(
                      "Select previously uploaded documents to add to the knowledge for this Agent."
                    )
                  }}
                </p>
                <el-table
                  :data="fileLibrary"
                  height="250"
                  @selection-change="handleLibrarySelectionChange"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column prop="name" :label="$t('File Name')" />
                  <el-table-column
                    prop="type"
                    :label="$t('Type')"
                    width="100"
                  />
                  <el-table-column
                    prop="date"
                    :label="$t('Upload Date')"
                    width="180"
                  />
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.main-grid {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: 2rem;
}

.left-column {
  position: sticky;
  top: 2rem;
  height: calc(100vh - 4rem);
}

.card {
  background-color: #ffffff;
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.05),
    0 2px 4px -2px rgb(0 0 0 / 0.05);
  border: 1px solid #e2e8f0;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.avatar-uploader .el-upload {
  border: 2px dashed #d9d9d9;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 150px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.avatar {
  width: 150px;
  height: 150px;
  display: block;
  object-fit: cover;
}

.gemini-button {
  background: linear-gradient(to right, #8e44ad, #3498db);
  color: white;
  border: none;
}

.gemini-button:hover {
  opacity: 0.9;
}

.chat-preview-container {
  background-color: #f1f5f9;
  border-radius: 0.75rem;
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-preview-window {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.chat-preview-header {
  padding: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
}

.chat-preview-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 0.75rem;
}

.chat-preview-body {
  padding: 1rem;
  flex-grow: 1;
  font-size: 0.875rem;
}

.chat-bubble {
  background-color: #e2e8f0;
  color: #334155;
  padding: 0.5rem 0.75rem;
  border-radius: 1rem;
  border-bottom-left-radius: 0.25rem;
  max-width: 90%;
  display: inline-block;
  word-wrap: break-word;
}

.starter-pills-container {
  margin-top: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.starter-pill {
  background-color: transparent;
  border: 1px solid #cbd5e1;
  color: #475569;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  cursor: pointer;
  transition: all 0.2s;
}

.starter-pill:hover {
  background-color: #e2e8f0;
  border-color: #94a3b8;
}

@media (max-width: 1024px) {
  .main-grid {
    grid-template-columns: 1fr;
  }
  .left-column {
    position: static;
    height: auto;
  }
}
</style>
