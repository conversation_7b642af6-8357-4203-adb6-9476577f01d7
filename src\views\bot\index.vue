<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useBotHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

// Lazy load components
const BotDrawerForm = defineAsyncComponent(
  () => import("./components/BotDrawerForm.vue")
);

const BotFilterForm = defineAsyncComponent(
  () => import("./components/BotFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

// Sử dụng hook ở đây thay vì trong từng component (như chat module)
const botHook = useBotHook();

// Methods
const onSearch = () => {
  botHook.fnGetBots();
};

// resetForm không cần nữa vì đã có trong botHook

const openDialog = (title = $t("Add"), row?: any) => {
  botHook.drawerValues.value = row ? clone(row, true) : { status: "active" };
  botHook.drawerVisible.value = true;
  nextTick(() => {
    botHook.botFormRef.value?.resetFields();
  });
};

onMounted(() => {
  botHook.fnGetBots();
});
</script>

<template>
  <div class="main">
    <div ref="contentRef">
      <PureTableBar
        :title="$t('Bot Management')"
        :columns="columns"
        @refresh="onSearch"
      >
        <template #buttons>
          <el-button
            type="primary"
            :icon="useRenderIcon('ep:plus')"
            @click="openDialog()"
          >
            {{ $t("Add") }}
          </el-button>
          <el-button
            :icon="useRenderIcon('ri:delete-bin-line')"
            :disabled="!botHook.multipleSelection.value.length"
            @click="botHook.handleBulkDelete(tableRef)"
          >
            {{ $t("Bulk Delete") }}
          </el-button>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <div
            v-if="!deviceDetection().isMobile"
            class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
          >
            <!-- Filter form sẽ được hiển thị qua drawer -->
          </div>
          <PureTable
            ref="tableRef"
            row-key="id"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            align-whole="center"
            table-layout="auto"
            :loading="botHook.loading.value"
            :size="size"
            :data="botHook.records.value"
            :columns="dynamicColumns"
            :pagination="botHook.pagination"
            :paginationSmall="size === 'small'"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @selection-change="botHook.fnHandleSelectionChange"
            @page-size-change="botHook.fnHandleSizeChange"
            @page-current-change="botHook.fnHandlePageChange"
            @sort-change="botHook.fnHandleSortChange"
          >
            <template #operation="{ row }">
              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                :icon="useRenderIcon('ep:edit-pen')"
                @click="openDialog($t('Edit'), row)"
              >
                {{ $t("Edit") }}
              </el-button>
              <el-popconfirm
                :title="$t('Are you sure to delete this item?')"
                @confirm="botHook.handleDelete(row.id)"
              >
                <template #reference>
                  <el-button
                    class="reset-margin"
                    link
                    type="primary"
                    :size="size"
                    :icon="useRenderIcon('ep:delete')"
                  >
                    {{ $t("Delete") }}
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <BotDrawerForm :bot-hook="botHook" />

    <BotFilterForm :bot-hook="botHook" />
  </div>
</template>
