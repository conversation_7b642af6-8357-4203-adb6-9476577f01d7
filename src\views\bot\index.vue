<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useBotHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { IconifyIconOnline } from "@/components/ReIcon";

// Lazy load components
const BotDrawerForm = defineAsyncComponent(
  () => import("./components/BotDrawerForm.vue")
);

const BotFilterForm = defineAsyncComponent(
  () => import("./components/BotFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

// Sử dụng hook ở đây thay vì trong từng component (như chat module)
const botHook = useBotHook();

// Methods
const onSearch = () => {
  botHook.fnGetBots();
};

// resetForm không cần nữa vì đã có trong botHook

const openDialog = (title = $t("Add"), row?: any) => {
  botHook.drawerValues.value = row ? clone(row, true) : { status: "active" };
  botHook.drawerVisible.value = true;
  nextTick(() => {
    botHook.botFormRef.value?.resetFields();
  });
};

onMounted(() => {
  botHook.fnGetBots();
});
</script>

<template>
  <div class="main">
    <div ref="contentRef">
      <PureTableBar
        :title="$t('Bot Management')"
        :columns="columns"
        @refresh="onSearch"
      >
        <template #buttons>
          <el-tooltip :content="$t('Add')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="!hasAuth('bot.create')"
              @click="openDialog()"
            >
              <IconifyIconOnline
                :icon="
                  hasAuth('bot.create')
                    ? 'flat-color-icons:plus'
                    : 'icons8:plus'
                "
                width="18px"
              />
            </el-button>
          </el-tooltip>
          <el-tooltip :content="$t('Delete')" placement="top">
            <el-button
              type="text"
              class="font-bold text-[16px]"
              :disabled="
                botHook.multipleSelection.value.length === 0 ||
                (botHook.multipleSelection.value.length > 0 &&
                  !hasAuth('bot.delete'))
              "
              @click="botHook.handleBulkDelete"
            >
              <IconifyIconOnline
                icon="tabler:trash-x-filled"
                width="18px"
                class="text-red-600"
              />
            </el-button>
          </el-tooltip>
        </template>

        <template v-slot="{ size, dynamicColumns }">
          <div
            v-if="!deviceDetection().isMobile"
            class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
          >
            <!-- Filter form sẽ được hiển thị qua drawer -->
          </div>
          <PureTable
            ref="tableRef"
            row-key="id"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            align-whole="center"
            table-layout="auto"
            :loading="botHook.loading.value"
            :size="size"
            :data="botHook.records.value"
            :columns="dynamicColumns"
            :pagination="botHook.pagination"
            :paginationSmall="size === 'small'"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @selection-change="botHook.fnHandleSelectionChange"
            @page-size-change="botHook.fnHandleSizeChange"
            @page-current-change="botHook.fnHandlePageChange"
            @sort-change="botHook.fnHandleSortChange"
          >
            <template #operation="{ row }">
              <el-dropdown>
                <IconifyIconOnline
                  icon="ep:more-filled"
                  width="18px"
                  class="cursor-pointer"
                />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="() => {}">
                      <IconifyIconOnline
                        icon="material-symbols:visibility"
                        class="text-green-600"
                      />
                      <span class="ml-2">
                        {{ $t("View") }}
                      </span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      :disabled="!hasAuth('bot.update')"
                      @click="openDialog($t('Edit'), row)"
                    >
                      <IconifyIconOnline
                        icon="material-symbols:edit"
                        class="text-blue-600"
                      />
                      <span class="ml-2">
                        {{ $t("Edit") }}
                      </span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      :disabled="!hasAuth('bot.delete')"
                      @click="botHook.handleDelete(row.id)"
                    >
                      <IconifyIconOnline
                        icon="tabler:trash"
                        class="text-red-800"
                      />
                      <span class="ml-2">
                        {{ $t("Delete") }}
                      </span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <BotDrawerForm :bot-hook="botHook" />

    <BotFilterForm :bot-hook="botHook" />
  </div>
</template>
