<script setup lang="ts">
import { ref, nextTick, onMounted, defineAsyncComponent } from "vue";
import { useBotHook } from "./utils/hook";
import { PureTableBar } from "@/components/RePureTableBar";
import { columns } from "./utils/columns";
import { clone, deviceDetection } from "@pureadmin/utils";
import { $t } from "@/plugins/i18n";
import PureTable from "@pureadmin/table";
import { hasAuth } from "@/router/utils";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

// Lazy load components
const BotDrawerForm = defineAsyncComponent(
  () => import("./components/BotDrawerForm.vue")
);

const BotFilterForm = defineAsyncComponent(
  () => import("./components/BotFilterForm.vue")
);

const tableRef = ref();
const contentRef = ref();

const {
  // Data/State
  loading,
  filterRef,
  pagination,
  records,
  multipleSelection,
  // Event handlers
  handleBulkDelete,
  handleDelete,
  fnGetBots,
  fnHandlePageChange,
  fnHandleSelectionChange,
  fnHandleSortChange,
  fnHandleSizeChange,
  // Form related
  filterVisible,
  drawerVisible,
  drawerValues,
  botFormRef,
  handleSubmit,
  handleFilter
} = useBotHook();

// Methods
const onSearch = () => {
  fnGetBots();
};

const resetForm = formEl => {
  if (!formEl) return;
  formEl.resetFields();
  fnGetBots();
};

const openDialog = (title = $t("Add"), row?: any) => {
  drawerValues.value = row ? clone(row, true) : { status: "active" };
  drawerVisible.value = true;
  nextTick(() => {
    botFormRef.value?.setOptions(drawerValues.value);
  });
};

onMounted(() => {
  fnGetBots();
});
</script>

<template>
  <div class="main">
    <div ref="contentRef">
      <PureTableBar
        :title="$t('Bot Management')"
        :columns="columns"
        @refresh="onSearch"
      >
        <template #buttons>
          <el-button
            type="primary"
            :icon="useRenderIcon('ep:plus')"
            @click="openDialog()"
          >
            {{ $t("Add") }}
          </el-button>
          <el-button
            :icon="useRenderIcon('ri:delete-bin-line')"
            :disabled="!multipleSelection.length"
            @click="handleBulkDelete(tableRef)"
          >
            {{ $t("Bulk Delete") }}
          </el-button>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <div
            v-if="!deviceDetection().isMobile"
            class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
          >
            <BotFilterForm
              ref="formRef"
              :visible="false"
              :values="filterRef"
              @submit="onSearch"
              @reset="resetForm"
            />
          </div>
          <PureTable
            ref="tableRef"
            row-key="id"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            align-whole="center"
            table-layout="auto"
            :loading="loading"
            :size="size"
            :data="records"
            :columns="dynamicColumns"
            :pagination="pagination"
            :paginationSmall="size === 'small'"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @selection-change="fnHandleSelectionChange"
            @page-size-change="fnHandleSizeChange"
            @page-current-change="fnHandlePageChange"
            @sort-change="fnHandleSortChange"
          >
            <template #operation="{ row }">
              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                :icon="useRenderIcon('ep:edit-pen')"
                @click="openDialog($t('Edit'), row)"
              >
                {{ $t("Edit") }}
              </el-button>
              <el-popconfirm
                :title="$t('Are you sure to delete this item?')"
                @confirm="handleDelete(row.id)"
              >
                <template #reference>
                  <el-button
                    class="reset-margin"
                    link
                    type="primary"
                    :size="size"
                    :icon="useRenderIcon('ep:delete')"
                  >
                    {{ $t("Delete") }}
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </PureTable>
        </template>
      </PureTableBar>
    </div>

    <BotDrawerForm
      ref="botFormRef"
      v-model:visible="drawerVisible"
      :values="drawerValues"
      @submit="handleSubmit"
    />

    <BotFilterForm
      v-model:visible="filterVisible"
      :values="filterRef"
      @submit="handleFilter"
    />
  </div>
</template>
